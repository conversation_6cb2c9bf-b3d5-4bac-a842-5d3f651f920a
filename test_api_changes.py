#!/usr/bin/env python3
"""
Test script to verify the API changes for collection_name parameter
"""

import requests
import json
import sys

# API base URL
API_BASE_URL = "http://localhost:8000"

def test_search_endpoint():
    """Test the unified /search endpoint with collection_name parameter"""
    print("Testing /search endpoint...")
    
    # Test basic search with collection_name
    search_data = {
        "query": "test question",
        "collection_name": "ag_discussion_forum",
        "top_k": 5
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/search", json=search_data)
        print(f"Search response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Search successful! Found {result['total_results']} results")
            print(f"Collection used: {result['parameters']['collection']}")
            return True
        else:
            print(f"Search failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to API. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"Error testing search: {e}")
        return False

def test_search_with_metadata():
    """Test search with metadata filters"""
    print("\nTesting /search endpoint with metadata filters...")
    
    search_data = {
        "query": "calculus problems",
        "collection_name": "ag_discussion_dev",
        "top_k": 3,
        "metadata_filters": {
            "type": "calculation"
        },
        "metadata_search": {
            "tags": "math"
        }
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/search", json=search_data)
        print(f"Metadata search response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Metadata search successful! Found {result['total_results']} results")
            print(f"Metadata filters applied: {result['parameters']['metadata_filters']}")
            return True
        else:
            print(f"Metadata search failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error testing metadata search: {e}")
        return False

def test_add_question_endpoint():
    """Test the /add_question endpoint with collection_name parameter"""
    print("\nTesting /add_question endpoint...")
    
    question_data = {
        "question_id": 99999,
        "question": "What is the derivative of x^2?",
        "collection_name": "test_collection",
        "type": "calculation",
        "subject": "calculus"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/add_question", json=question_data)
        print(f"Add question response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Question added successfully to collection: {question_data['collection_name']}")
            print(f"Question ID: {result['question_id']}")
            return True
        else:
            print(f"Add question failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error testing add question: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint to ensure API is running"""
    print("Testing /health endpoint...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("API is healthy!")
            return True
        else:
            print(f"Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to API. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"Error testing health: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Testing API Changes ===\n")
    
    # Test health first
    if not test_health_endpoint():
        print("\nAPI is not running. Please start the server first.")
        sys.exit(1)
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    if test_search_endpoint():
        tests_passed += 1
    
    if test_search_with_metadata():
        tests_passed += 1
        
    if test_add_question_endpoint():
        tests_passed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! API changes are working correctly.")
    else:
        print("❌ Some tests failed. Please check the API implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()

# API Changes Summary: Collection Name Parameter

## Overview
Modified the Discussion Forum RAG API to add `collection_name` parameter to both search and add question endpoints, allowing users to specify which Qdrant collection to operate on.

## Changes Made

### 1. Updated API Models (`api_models.py`)

#### SearchRequest Model
- **Added**: `collection_name: str` field (required)
- **Added**: `metadata_filters: Optional[Dict[str, Any]]` field for exact matching with OR logic
- **Added**: `metadata_search: Optional[Dict[str, str]]` field for text search within metadata
- **Removed**: `SearchDiscussionRequest` class (no longer needed)

#### AddQuestionRequest Model
- **Already had**: `collection_name: str` field
- **Updated**: `get_additional_fields()` method to exclude `collection_name` from additional fields

### 2. Updated API Endpoints (`api.py`)

#### Unified `/search` Endpoint
- **Modified**: Now accepts `collection_name` parameter to specify which collection to search
- **Added**: Support for metadata filtering using `metadata_filters` and `metadata_search`
- **Enhanced**: Automatically chooses between basic search and metadata-filtered search based on provided parameters
- **Updated**: Documentation with comprehensive examples and parameter descriptions

#### `/add_question` Endpoint
- **Modified**: Now uses `collection_name` from request instead of hardcoded "ag_discussion_dev"
- **Added**: Proper collection switching with restoration in finally block
- **Updated**: Success message to include collection name

#### Removed `/search_discussion` Endpoint
- **Removed**: The separate `/search_discussion` endpoint since functionality is now unified in `/search`

### 3. Key Features

#### Collection Flexibility
- Users can now specify any collection name for both search and add operations
- No longer limited to hardcoded collection names
- Proper collection switching with automatic restoration

#### Metadata Filtering
- Support for exact match filters with OR logic
- Support for text search within metadata fields
- Backward compatible - works with or without metadata filters

#### Unified Search Interface
- Single `/search` endpoint handles all search scenarios
- Supports both basic semantic search and advanced metadata filtering
- Maintains all existing functionality while adding new capabilities

## API Usage Examples

### Basic Search
```json
POST /search
{
  "query": "derivative calculus",
  "collection_name": "ag_discussion_forum"
}
```

### Search with Metadata Filters
```json
POST /search
{
  "query": "integration problems",
  "collection_name": "ag_discussion_dev",
  "top_k": 10,
  "metadata_filters": {
    "type": "calculation",
    "difficulty": "intermediate"
  },
  "metadata_search": {
    "tags": "calculus"
  }
}
```

### Add Question to Specific Collection
```json
POST /add_question
{
  "question_id": 12345,
  "question": "What is the derivative of x^2?",
  "collection_name": "my_custom_collection",
  "type": "calculation",
  "subject": "calculus"
}
```

## Backward Compatibility
- All existing functionality is preserved
- API responses maintain the same structure
- Additional fields are optional and don't break existing clients

## Testing
- Created `test_api_changes.py` script to verify functionality
- Tests cover basic search, metadata search, and add question operations
- Includes health check and error handling

## Benefits
1. **Flexibility**: Users can work with any collection, not just predefined ones
2. **Unified Interface**: Single search endpoint for all use cases
3. **Enhanced Filtering**: Powerful metadata search capabilities
4. **Maintainability**: Reduced code duplication by removing separate endpoints
5. **User Control**: Collection selection is now user-driven rather than hardcoded

## Migration Notes
- Existing clients need to add `collection_name` parameter to search requests
- The `/search_discussion` endpoint has been removed - use `/search` instead
- All other functionality remains unchanged

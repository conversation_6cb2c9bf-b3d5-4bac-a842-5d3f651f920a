#!/usr/bin/env python3
"""
FastAPI application for Discussion Forum RAG System
"""

import time
import logging
from datetime import datetime
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from qdrant_client import QdrantClient
from qdrant_client.http.exceptions import UnexpectedResponse
from qdrant_client.models import VectorParams, Distance

from config import settings
from api_models import (
    HealthResponse, SearchRequest, SearchResponse, QuestionResult,
    QuestionDetailResponse, ErrorResponse, AddQuestionRequest, AddQuestionResponse
)
from rag_system_openai import QuestionRAGOpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Global variables for RAG system
rag_system: Optional[QuestionRAGOpenAI] = None
qdrant_client: Optional[QdrantClient] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global rag_system, qdrant_client
    
    try:
        logger.info("Initializing RAG system...")
        
        # Initialize Qdrant client
        qdrant_client = QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            timeout=settings.qdrant_timeout
        )
        
        # Initialize RAG system
        rag_system = QuestionRAGOpenAI(
            openai_api_key=settings.get_openai_api_key(),
            qdrant_client=qdrant_client,
            collection_name=settings.qdrant_collection
        )
        
        logger.info("RAG system initialized successfully")
        
        # Verify collection exists
        try:
            collection_info = qdrant_client.get_collection(settings.qdrant_collection)
            logger.info(f"Connected to collection '{settings.qdrant_collection}' with {collection_info.points_count} points")
        except Exception as e:
            logger.warning(f"Could not verify collection: {e}")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise
    finally:
        logger.info("Shutting down RAG system...")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="API for searching and retrieving questions using RAG with Qdrant vector store",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# Add rate limiting
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


def get_rag_system() -> QuestionRAGOpenAI:
    """Dependency to get RAG system instance."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    return rag_system


def get_qdrant_client() -> QdrantClient:
    """Dependency to get Qdrant client instance."""
    if qdrant_client is None:
        raise HTTPException(status_code=503, detail="Qdrant client not initialized")
    return qdrant_client


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="InternalServerError",
            message="An unexpected error occurred",
            detail=str(exc) if settings.debug else None,
            timestamp=datetime.utcnow().isoformat()
        ).dict()
    )

@app.get("/health", response_model=HealthResponse)
@limiter.limit(f"{settings.rate_limit_requests}/minute")
async def health_check(request: Request):
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        message="API is running",
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version
    )


@app.post("/search", response_model=SearchResponse)
# @limiter.limit(f"{settings.rate_limit_requests}/minute")
async def search_questions(
    request: Request,
    search_request: SearchRequest,
    rag: QuestionRAGOpenAI = Depends(get_rag_system)
):
    """
    Search questions from any specified collection with optional metadata filtering.

    This unified endpoint allows searching from any collection by specifying the collection_name.
    It supports both semantic search on question content and filtering based on metadata fields.

    ## Request Parameters

    ### query (string, required)
    - **Description**: The search query text to find similar questions
    - **Format**: Plain text string (supports English, Nepali, and mixed content)
    - **Length**: Any length (automatically truncated to 1000 characters if longer)
    - **Examples**:
      - "compound interest calculation"
      - "ब्याज गणना" (Interest calculation in Nepali)
      - "mathematical sequence problems"

    ### collection_name (string, required)
    - **Description**: Name of the Qdrant collection to search in
    - **Examples**:
      - `"ag_discussion_forum"`: For searching forum questions
      - `"ag_discussion_dev"`: For searching development questions
      - `"custom_collection"`: For searching custom collections

    ### top_k (integer, optional)
    - **Description**: Maximum number of similar questions to return
    - **Range**: 1-50
    - **Default**: 5

    ### score_threshold (float, optional)
    - **Description**: Minimum similarity score required for results (cosine similarity)
    - **Range**: 0.0-1.0
    - **Default**: 0.7

    ### include_content (boolean, optional)
    - **Description**: Whether to include the full question content in response
    - **Default**: true

    ### max_content_length (integer, optional)
    - **Description**: Maximum characters to return in content preview
    - **Range**: 100-2000
    - **Default**: 500

    ### metadata_filters (object, optional)
    - **Description**: Metadata filters for exact matching with OR logic
    - **Example**: `{"type": "calculation", "subject": "calculus"}` finds questions that are EITHER calculation type OR calculus subject

    ### metadata_search (object, optional)
    - **Description**: Metadata text search
    - **Example**: `{"tags": "math", "author": "teacher"}`

    ## Example Requests

    ### Basic Search
    ```json
    {
      "query": "derivative calculus",
      "collection_name": "ag_discussion_forum"
    }
    ```

    ### Search with Metadata Filters
    ```json
    {
      "query": "integration problems",
      "collection_name": "ag_discussion_dev",
      "top_k": 10,
      "metadata_filters": {
        "type": "calculation",
        "difficulty": "intermediate"
      },
      "metadata_search": {
        "tags": "calculus"
      }
    }
    ```

    ### Search by Subject and Author
    ```json
    {
      "query": "physics problems",
      "collection_name": "ag_discussion_dev",
      "metadata_filters": {
        "subject": "physics",
        "author": "physics_teacher"
      },
      "score_threshold": 0.6
    }
    ```

    ## Error Responses

    - **400 Bad Request**: Invalid parameters (empty query, top_k out of range, etc.)
    - **500 Internal Server Error**: Search system failure or OpenAI API issues
    - **503 Service Unavailable**: RAG system not initialized or Qdrant connection failed
    """
    start_time = time.time()

    try:
        # Query is already validated and truncated by Pydantic model
        query = search_request.query

        # Change collection to search from specified collection
        original_collection = rag.collection_name
        rag.collection_name = search_request.collection_name

        try:
            # Check if metadata filters are provided
            if search_request.metadata_filters or search_request.metadata_search:
                # Perform search with metadata filtering
                results = rag.search_with_metadata_filters(
                    query=query,
                    top_k=search_request.top_k if search_request.top_k is not None else settings.default_top_k,
                    score_threshold=search_request.score_threshold if search_request.score_threshold is not None else settings.default_score_threshold,
                    metadata_filters=search_request.metadata_filters,
                    metadata_search=search_request.metadata_search
                )
            else:
                # Perform basic search
                results = rag.search_related_questions(
                    query=query,
                    top_k=search_request.top_k if search_request.top_k is not None else settings.default_top_k,
                    score_threshold=search_request.score_threshold if search_request.score_threshold is not None else settings.default_score_threshold
                )
        finally:
            # Restore original collection name
            rag.collection_name = original_collection
        
        # Format results for response
        formatted_results = []
        for result in results:
            question_result = QuestionResult(
                question_id=result["question_id"],
                similarity_score=result["similarity_score"],
                question=result["question"][:search_request.max_content_length] if search_request.include_content else "",
                text_length=result["text_length"]
            )

            # Add dynamic metadata fields
            for key, value in result.items():
                if key not in ["question_id", "similarity_score", "question", "text_length"]:
                    setattr(question_result, key, value)

            formatted_results.append(question_result)
        
        search_time = (time.time() - start_time) * 1000

        logger.info(f"Search completed in {search_time:.2f}ms, found {len(formatted_results)} results")

        return SearchResponse(
            query=query,
            total_results=len(formatted_results),
            results=formatted_results,
            search_time_ms=round(search_time, 2),
            parameters={
                "top_k": search_request.top_k if search_request.top_k is not None else settings.default_top_k,
                "score_threshold": search_request.score_threshold if search_request.score_threshold is not None else settings.default_score_threshold,
                "include_content": search_request.include_content,
                "max_content_length": search_request.max_content_length,
                "query_truncated": search_request.was_query_truncated(),
                "metadata_filters": search_request.metadata_filters,
                "metadata_search": search_request.metadata_search,
                "collection": search_request.collection_name
            }
        )
        
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/add_question", response_model=AddQuestionResponse)
async def add_question(
    request: Request,
    add_request: AddQuestionRequest,
    rag: QuestionRAGOpenAI = Depends(get_rag_system)
):
    """
    Add a new question to the Qdrant collection with dynamic fields.

    This endpoint allows you to add individual questions to the vector database.
    The question will be embedded using OpenAI's text-embedding-3-large model
    and stored in the Qdrant collection for future similarity searches.

    **Required Fields:**
    - `question_id`: Unique integer identifier for the question
    - `question`: The question content (LaTeX mathematical notation preserved)

    **Dynamic Fields:**
    You can add any additional fields directly to the request body. Common examples:
    - `type`: Question type (e.g., "multiple_choice", "essay", "calculation")
    - `subject`: Subject area (e.g., "mathematics", "physics", "chemistry")
    - `difficulty`: Difficulty level (e.g., "beginner", "intermediate", "advanced")
    - `topic`: Specific topic (e.g., "derivatives", "integration", "limits")
    - `tags`: Array of tags
    - `author`: Question author
    - `created_at`: Creation timestamp

    **Example Request:**
    ```json
    {
        "question_id": 12345,
        "question": "What is the derivative of $f(x) = x^2 + 3x + 2$?",
        "type": "calculation",
        "subject": "calculus",
        "difficulty": "beginner",
        "topic": "derivatives",
        "tags": ["math", "calculus", "derivatives"],
        "author": "teacher123"
    }
    ```

    **Returns:**
    - Success confirmation with question ID and all stored fields
    - Error details if the operation fails
    """
    start_time = time.time()

    # Change collection to the specified collection
    original_collection = rag.collection_name
    rag.collection_name = add_request.collection_name

    try:
        logger.info(f"Adding question {add_request.question_id} to collection '{rag.collection_name}'")

        # Check if question already exists
        try:
            existing_point = rag.client.retrieve(
                collection_name=rag.collection_name,
                ids=[add_request.question_id]
            )
            if existing_point:
                logger.warning(f"Question {add_request.question_id} already exists, will update")
        except Exception:
            # Point doesn't exist, which is fine
            pass

        # Get additional fields from the request
        additional_fields = add_request.get_additional_fields()

        # Add the question to the collection
        result = rag.add_single_question(
            question_id=add_request.question_id,
            question_text=add_request.question,
            additional_fields=additional_fields
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to add question to collection: {result.get('error', 'Unknown error')}"
            )

        processing_time = (time.time() - start_time) * 1000
        logger.info(f"Successfully added question {add_request.question_id} in {processing_time:.2f}ms")

        return AddQuestionResponse(
            success=True,
            question_id=add_request.question_id,
            message=f"Question {add_request.question_id} successfully added to collection '{add_request.collection_name}'",
            embedding_created=True,
            stored_fields=result["payload"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding question {add_request.question_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to add question: {str(e)}"
        )
    finally:
        # Restore original collection name
        rag.collection_name = original_collection


# @app.post("/create_collection")
# @limiter.limit("5/minute")
# async def create_collection(
#     request: Request,
#     collection_name: Optional[str] = None,
#     qdrant: QdrantClient = Depends(get_qdrant_client)
# ):
#     """
#     Create an empty Qdrant collection for storing questions.

#     This endpoint creates a new collection in Qdrant with the proper configuration
#     for storing question embeddings using OpenAI's text-embedding-3-large model.

#     **Parameters:**
#     - `collection_name`: Optional collection name (defaults to 'ag_discussion')

#     **Rate Limit:** 5 requests per minute per IP address

#     **Returns:**
#     - Success confirmation with collection details
#     - Error if collection already exists or creation fails
#     """
#     try:
#         # Use default collection name if not provided
#         target_collection = collection_name or settings.qdrant_collection

#         logger.info(f"Creating collection '{target_collection}'")

#         # Check if collection already exists
#         try:
#             existing_collection = qdrant.get_collection(target_collection)
#             return JSONResponse(
#                 status_code=200,
#                 content={
#                     "success": True,
#                     "message": f"Collection '{target_collection}' already exists",
#                     "collection_name": target_collection,
#                     "points_count": existing_collection.points_count,
#                     "vector_size": existing_collection.config.params.vectors.size
#                 }
#             )
#         except Exception:
#             # Collection doesn't exist, create it
#             pass

#         # Create new collection with OpenAI text-embedding-3-large configuration
#         qdrant.create_collection(
#             collection_name=target_collection,
#             vectors_config=VectorParams(
#                 size=1536,  # text-embedding-3-large dimension
#                 distance=Distance.COSINE
#             )
#         )

#         logger.info(f"Successfully created collection '{target_collection}'")

#         return JSONResponse(
#             status_code=201,
#             content={
#                 "success": True,
#                 "message": f"Collection '{target_collection}' created successfully",
#                 "collection_name": target_collection,
#                 "vector_size": 1536,
#                 "distance_metric": "cosine",
#                 "points_count": 0
#             }
#         )

#     except Exception as e:
#         logger.error(f"Error creating collection: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Failed to create collection: {str(e)}"
#         )




